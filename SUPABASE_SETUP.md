n# Supabase Setup Guide

This guide will help you set up Supabase authentication for your Vue 3 dashboard application.

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign up or log in to your account
3. Click "New Project"
4. Choose your organization
5. Fill in your project details:
   - Name: `vue-auth-dashboard`
   - Database Password: (choose a strong password)
   - Region: (choose closest to your users)
6. Click "Create new project"

## 2. Get Your Project Credentials

1. Go to your project dashboard
2. Click on "Settings" in the sidebar
3. Click on "API" in the settings menu
4. Copy the following values:
   - **Project URL** (something like `https://xxxxx.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

## 3. Configure Environment Variables

1. Open the `.env.local` file in your project root
2. Replace the placeholder values with your actual Supabase credentials:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

## 4. Set Up Database Schema

1. Go to your Supabase project dashboard
2. Click on "SQL Editor" in the sidebar
3. Click "New Query"
4. Copy and paste the SQL schema from `supabase-schema.sql`
5. Click "Run" to execute the schema

## 5. Configure Authentication

1. Go to "Authentication" in your Supabase dashboard
2. Click on "Settings"
3. Configure the following:
   - **Site URL**: `http://localhost:5173` (for development)
   - **Redirect URLs**: `http://localhost:5173/**` (for development)

For production, add your production URLs.

## 6. Enable Email Authentication

1. In Authentication > Settings
2. Make sure "Enable email confirmations" is turned ON
3. Configure email templates if needed

## 7. Test the Integration

1. Start your development server: `npm run dev`
2. Go to `http://localhost:5173`
3. Try registering a new account
4. Check your email for confirmation (if email confirmations are enabled)
5. Try logging in with your credentials

## 8. Optional: Set Up Row Level Security (RLS)

The provided schema includes RLS policies, but you can customize them:

1. Go to "Authentication" > "Policies"
2. Review and modify the policies for the `profiles` table as needed

## Troubleshooting

### Common Issues:

1. **"Invalid API key"**: Double-check your environment variables
2. **CORS errors**: Make sure your Site URL is configured correctly
3. **Email not sending**: Check your email settings in Authentication > Settings
4. **Database errors**: Verify the schema was applied correctly

### Environment Variables Not Loading:

Make sure your `.env.local` file is in the project root and restart your development server.

### Database Connection Issues:

1. Check your Project URL is correct
2. Verify your database is running (green status in dashboard)
3. Check the SQL Editor for any schema errors

## Next Steps

- Customize the user profile fields in the `profiles` table
- Set up email templates in Authentication > Templates
- Configure social authentication providers if needed
- Set up database backups and monitoring

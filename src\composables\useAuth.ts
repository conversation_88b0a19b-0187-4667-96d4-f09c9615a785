import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

export const useAuth = () => {
  const authStore = useAuthStore()
  const router = useRouter()

  const loginAndRedirect = async (credentials: { email: string; password: string }) => {
    try {
      await authStore.login(credentials)
      router.push('/dashboard')
    } catch (error) {
      // Error is handled in the store
      throw error
    }
  }

  const registerAndRedirect = async (credentials: {
    name: string
    email: string
    password: string
    confirmPassword: string
  }) => {
    try {
      await authStore.register(credentials)
      router.push('/dashboard')
    } catch (error) {
      // Error is handled in the store
      throw error
    }
  }

  const logoutAndRedirect = () => {
    authStore.logout()
    router.push('/login')
  }

  return {
    ...authStore,
    loginAndRedirect,
    registerAndRedirect,
    logoutAndRedirect
  }
}

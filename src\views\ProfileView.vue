<template>
  <DashboardLayout>
    <div class="space-y-6">
      <!-- Page header -->
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Profile</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage your personal information and account settings
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile picture -->
        <div class="lg:col-span-1">
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Profile Picture</h3>
              <div class="flex flex-col items-center">
                <img
                  class="h-24 w-24 rounded-full"
                  :src="
                    user?.avatar ||
                    'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=96&h=96&fit=crop&crop=face'
                  "
                  :alt="user?.name"
                />
                <button
                  class="mt-4 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Change Photo
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Profile form -->
        <div class="lg:col-span-2">
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Personal Information</h3>
              <form @submit.prevent="updateProfile" class="space-y-4">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">
                      Full Name
                    </label>
                    <input
                      id="name"
                      v-model="name.value.value"
                      type="text"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      :class="{ 'border-red-500': name.error.value }"
                    />
                    <p v-if="name.error.value" class="mt-1 text-sm text-red-600">
                      {{ name.error.value }}
                    </p>
                  </div>

                  <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">
                      Email Address
                    </label>
                    <input
                      id="email"
                      v-model="email.value.value"
                      type="email"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      :class="{ 'border-red-500': email.error.value }"
                    />
                    <p v-if="email.error.value" class="mt-1 text-sm text-red-600">
                      {{ email.error.value }}
                    </p>
                  </div>
                </div>

                <div>
                  <label for="bio" class="block text-sm font-medium text-gray-700"> Bio </label>
                  <textarea
                    id="bio"
                    v-model="bio"
                    rows="3"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    placeholder="Tell us about yourself..."
                  ></textarea>
                </div>

                <div class="pt-4">
                  <button
                    type="submit"
                    :disabled="isLoading"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {{ isLoading ? 'Updating...' : 'Update Profile' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- Change password -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Change Password</h3>
          <form @submit.prevent="changePassword" class="space-y-4">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label for="currentPassword" class="block text-sm font-medium text-gray-700">
                  Current Password
                </label>
                <input
                  id="currentPassword"
                  v-model="currentPassword.value.value"
                  type="password"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  :class="{ 'border-red-500': currentPassword.error.value }"
                />
                <p v-if="currentPassword.error.value" class="mt-1 text-sm text-red-600">
                  {{ currentPassword.error.value }}
                </p>
              </div>

              <div>
                <label for="newPassword" class="block text-sm font-medium text-gray-700">
                  New Password
                </label>
                <input
                  id="newPassword"
                  v-model="newPassword.value.value"
                  type="password"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  :class="{ 'border-red-500': newPassword.error.value }"
                />
                <p v-if="newPassword.error.value" class="mt-1 text-sm text-red-600">
                  {{ newPassword.error.value }}
                </p>
              </div>

              <div>
                <label for="confirmNewPassword" class="block text-sm font-medium text-gray-700">
                  Confirm New Password
                </label>
                <input
                  id="confirmNewPassword"
                  v-model="confirmNewPassword.value.value"
                  type="password"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  :class="{ 'border-red-500': confirmNewPassword.error.value }"
                />
                <p v-if="confirmNewPassword.error.value" class="mt-1 text-sm text-red-600">
                  {{ confirmNewPassword.error.value }}
                </p>
              </div>
            </div>

            <div>
              <button
                type="submit"
                :disabled="isPasswordLoading"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {{ isPasswordLoading ? 'Changing...' : 'Change Password' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </DashboardLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuth } from '@/composables/useAuth'
import { useValidation } from '@/composables/useValidation'
import DashboardLayout from '@/components/DashboardLayout.vue'

const { user } = useAuth()
const { createField, rules, validateForm } = useValidation()

const isLoading = ref(false)
const isPasswordLoading = ref(false)
const bio = ref('')

// Profile form fields
const name = createField('', [
  rules.required('Name is required'),
  rules.minLength(2, 'Name must be at least 2 characters'),
])

const email = createField('', [rules.required('Email is required'), rules.email()])

// Password form fields
const currentPassword = createField('', [rules.required('Current password is required')])

const newPassword = createField('', [
  rules.required('New password is required'),
  rules.minLength(6, 'Password must be at least 6 characters'),
])

const confirmNewPassword = createField('', [
  rules.required('Please confirm your new password'),
  rules.match(newPassword.value, 'Passwords do not match'),
])

const updateProfile = async () => {
  if (!validateForm([name, email])) {
    return
  }

  isLoading.value = true
  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    alert('Profile updated successfully!')
  } catch (error) {
    alert('Failed to update profile')
  } finally {
    isLoading.value = false
  }
}

const changePassword = async () => {
  if (!validateForm([currentPassword, newPassword, confirmNewPassword])) {
    return
  }

  isPasswordLoading.value = true
  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    alert('Password changed successfully!')

    // Reset form
    currentPassword.reset()
    newPassword.reset()
    confirmNewPassword.reset()
  } catch (error) {
    alert('Failed to change password')
  } finally {
    isPasswordLoading.value = false
  }
}

onMounted(() => {
  if (user) {
    name.value.value = user.name
    email.value.value = user.email
  }
})
</script>

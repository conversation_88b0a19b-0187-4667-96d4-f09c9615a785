<template>
  <div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Login Form -->
        <div class="flex items-center justify-center">
          <div class="max-w-md w-full space-y-8">
            <div>
              <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Sign in to your account
              </h2>
              <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <router-link
                  to="/register"
                  class="font-medium text-primary-600 hover:text-primary-500"
                >
                  create a new account
                </router-link>
              </p>
            </div>

            <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
              <div class="rounded-md shadow-sm -space-y-px">
                <div>
                  <label for="email" class="sr-only">Email address</label>
                  <input
                    id="email"
                    v-model="email.value.value"
                    type="email"
                    autocomplete="email"
                    required
                    class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                    :class="{ 'border-red-500': email.error.value }"
                    placeholder="Email address"
                  />
                  <p v-if="email.error.value" class="mt-1 text-sm text-red-600">
                    {{ email.error.value }}
                  </p>
                </div>

                <div>
                  <label for="password" class="sr-only">Password</label>
                  <input
                    id="password"
                    v-model="password.value.value"
                    type="password"
                    autocomplete="current-password"
                    required
                    class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                    :class="{ 'border-red-500': password.error.value }"
                    placeholder="Password"
                  />
                  <p v-if="password.error.value" class="mt-1 text-sm text-red-600">
                    {{ password.error.value }}
                  </p>
                </div>
              </div>

              <div v-if="error" class="rounded-md bg-red-50 p-4">
                <div class="flex">
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                      {{ error }}
                    </h3>
                  </div>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  :disabled="isLoading"
                  class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
                    <svg
                      class="animate-spin h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </span>
                  {{ isLoading ? 'Signing in...' : 'Sign in' }}
                </button>
              </div>

              <div class="text-sm text-center text-gray-600">
                <p class="mb-2">New to the platform?</p>
                <p>Create an account to get started with your dashboard.</p>
                <p class="mt-2 text-xs text-gray-500">
                  Note: You'll receive an email confirmation after registration.
                </p>
              </div>
            </form>
          </div>
        </div>

        <!-- Supabase Status -->
        <div class="flex items-center justify-center">
          <SupabaseStatus />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'
import { useValidation } from '@/composables/useValidation'
import SupabaseStatus from '@/components/SupabaseStatus.vue'

const { loginAndRedirect, isLoading, error, clearError } = useAuth()
const { createField, rules, validateForm } = useValidation()

const email = createField('', [rules.required('Email is required'), rules.email()])

const password = createField('', [rules.required('Password is required'), rules.minLength(6)])

const handleSubmit = async () => {
  clearError()

  if (!validateForm([email, password])) {
    return
  }

  try {
    await loginAndRedirect({
      email: email.value.value,
      password: password.value.value,
    })
  } catch (err) {
    // Error is handled by the auth store
  }
}
</script>

<template>
  <DashboardLayout>
    <div class="space-y-6">
      <!-- Page header -->
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-1 text-sm text-gray-500">
          Welcome back, {{ user?.name }}! Here's what's happening today.
        </p>
      </div>

      <!-- Stats cards -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div
          v-for="stat in stats"
          :key="stat.name"
          class="bg-white overflow-hidden shadow rounded-lg"
        >
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <component
                  :is="stat.icon"
                  class="h-6 w-6 text-gray-400"
                />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ stat.name }}
                  </dt>
                  <dd>
                    <div class="text-lg font-medium text-gray-900">
                      {{ stat.value }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span
                class="font-medium"
                :class="[
                  stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                ]"
              >
                {{ stat.changeType === 'increase' ? '+' : '-' }}{{ stat.change }}
              </span>
              <span class="text-gray-500"> from last month</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts and tables -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent activity -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Recent Activity
            </h3>
            <div class="mt-5">
              <div class="flow-root">
                <ul class="-mb-8">
                  <li
                    v-for="(activity, index) in recentActivity"
                    :key="activity.id"
                    class="relative pb-8"
                  >
                    <div v-if="index !== recentActivity.length - 1" class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                    <div class="relative flex space-x-3">
                      <div>
                        <span
                          class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white"
                          :class="activity.iconBackground"
                        >
                          <component
                            :is="activity.icon"
                            class="h-4 w-4 text-white"
                          />
                        </span>
                      </div>
                      <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p class="text-sm text-gray-500">
                            {{ activity.content }}
                          </p>
                        </div>
                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                          {{ activity.time }}
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick actions -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Quick Actions
            </h3>
            <div class="mt-5 grid grid-cols-1 gap-3">
              <button
                v-for="action in quickActions"
                :key="action.name"
                @click="action.action"
                class="relative group bg-white p-3 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 rounded-lg border border-gray-300 hover:bg-gray-50"
              >
                <div class="flex items-center">
                  <span
                    class="rounded-lg inline-flex p-2"
                    :class="action.iconBackground"
                  >
                    <component
                      :is="action.icon"
                      class="h-5 w-5"
                      :class="action.iconForeground"
                    />
                  </span>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900">
                      {{ action.name }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ action.description }}
                    </p>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </DashboardLayout>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'
import DashboardLayout from '@/components/DashboardLayout.vue'
import {
  UsersIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ClockIcon,
  PlusIcon,
  DocumentTextIcon,
  UserPlusIcon,
  CogIcon
} from '@heroicons/vue/24/outline'

const { user } = useAuth()

const stats = [
  {
    name: 'Total Users',
    value: '2,651',
    icon: UsersIcon,
    change: '12%',
    changeType: 'increase'
  },
  {
    name: 'Revenue',
    value: '$45,231',
    icon: CurrencyDollarIcon,
    change: '8%',
    changeType: 'increase'
  },
  {
    name: 'Active Sessions',
    value: '1,423',
    icon: ChartBarIcon,
    change: '3%',
    changeType: 'decrease'
  },
  {
    name: 'Avg. Response Time',
    value: '1.2s',
    icon: ClockIcon,
    change: '5%',
    changeType: 'decrease'
  }
]

const recentActivity = [
  {
    id: 1,
    content: 'New user registered',
    time: '2 hours ago',
    icon: UserPlusIcon,
    iconBackground: 'bg-green-500'
  },
  {
    id: 2,
    content: 'System backup completed',
    time: '4 hours ago',
    icon: DocumentTextIcon,
    iconBackground: 'bg-blue-500'
  },
  {
    id: 3,
    content: 'Settings updated',
    time: '6 hours ago',
    icon: CogIcon,
    iconBackground: 'bg-yellow-500'
  }
]

const quickActions = [
  {
    name: 'Add New User',
    description: 'Create a new user account',
    icon: UserPlusIcon,
    iconBackground: 'bg-green-100',
    iconForeground: 'text-green-600',
    action: () => console.log('Add user')
  },
  {
    name: 'Generate Report',
    description: 'Create a new analytics report',
    icon: DocumentTextIcon,
    iconBackground: 'bg-blue-100',
    iconForeground: 'text-blue-600',
    action: () => console.log('Generate report')
  },
  {
    name: 'System Settings',
    description: 'Configure system preferences',
    icon: CogIcon,
    iconBackground: 'bg-purple-100',
    iconForeground: 'text-purple-600',
    action: () => console.log('Open settings')
  }
]
</script>

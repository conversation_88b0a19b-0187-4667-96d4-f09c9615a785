import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  role: 'admin' | 'user'
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterCredentials {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // Mock users for demo purposes
  const mockUsers: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Admin User',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
      role: 'admin'
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Regular User',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
      role: 'user'
    }
  ]

  const login = async (credentials: LoginCredentials): Promise<void> => {
    isLoading.value = true
    error.value = null

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Mock authentication
      const foundUser = mockUsers.find(u => u.email === credentials.email)
      
      if (!foundUser || credentials.password !== 'password') {
        throw new Error('Invalid email or password')
      }

      user.value = foundUser
      localStorage.setItem('auth_token', 'mock_token_' + foundUser.id)
      localStorage.setItem('user', JSON.stringify(foundUser))
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (credentials: RegisterCredentials): Promise<void> => {
    isLoading.value = true
    error.value = null

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      if (credentials.password !== credentials.confirmPassword) {
        throw new Error('Passwords do not match')
      }

      if (mockUsers.find(u => u.email === credentials.email)) {
        throw new Error('Email already exists')
      }

      const newUser: User = {
        id: Date.now().toString(),
        email: credentials.email,
        name: credentials.name,
        role: 'user'
      }

      user.value = newUser
      localStorage.setItem('auth_token', 'mock_token_' + newUser.id)
      localStorage.setItem('user', JSON.stringify(newUser))
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Registration failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = (): void => {
    user.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user')
  }

  const initializeAuth = (): void => {
    const token = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('user')
    
    if (token && savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch {
        logout()
      }
    }
  }

  const clearError = (): void => {
    error.value = null
  }

  return {
    user,
    isLoading,
    error,
    isAuthenticated,
    isAdmin,
    login,
    register,
    logout,
    initializeAuth,
    clearError
  }
})

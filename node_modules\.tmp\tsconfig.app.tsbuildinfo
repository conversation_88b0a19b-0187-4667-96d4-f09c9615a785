{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/components/dashboardlayout.vue", "../../src/components/helloworld.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/composables/useauth.ts", "../../src/composables/usevalidation.ts", "../../src/router/index.ts", "../../src/stores/auth.ts", "../../src/stores/counter.ts", "../../src/views/aboutview.vue", "../../src/views/analyticsview.vue", "../../src/views/dashboardview.vue", "../../src/views/homeview.vue", "../../src/views/loginview.vue", "../../src/views/profileview.vue", "../../src/views/registerview.vue", "../../src/views/settingsview.vue", "../../src/views/usersview.vue"], "version": "5.8.3"}
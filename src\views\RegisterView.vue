<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <router-link
            to="/login"
            class="font-medium text-primary-600 hover:text-primary-500"
          >
            sign in to your existing account
          </router-link>
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
            <input
              id="name"
              v-model="name.value.value"
              type="text"
              autocomplete="name"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              :class="{ 'border-red-500': name.error.value }"
              placeholder="Enter your full name"
            />
            <p v-if="name.error.value" class="mt-1 text-sm text-red-600">
              {{ name.error.value }}
            </p>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
            <input
              id="email"
              v-model="email.value.value"
              type="email"
              autocomplete="email"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              :class="{ 'border-red-500': email.error.value }"
              placeholder="Enter your email address"
            />
            <p v-if="email.error.value" class="mt-1 text-sm text-red-600">
              {{ email.error.value }}
            </p>
          </div>
          
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
            <input
              id="password"
              v-model="password.value.value"
              type="password"
              autocomplete="new-password"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              :class="{ 'border-red-500': password.error.value }"
              placeholder="Create a password"
            />
            <p v-if="password.error.value" class="mt-1 text-sm text-red-600">
              {{ password.error.value }}
            </p>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">Confirm Password</label>
            <input
              id="confirmPassword"
              v-model="confirmPassword.value.value"
              type="password"
              autocomplete="new-password"
              required
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              :class="{ 'border-red-500': confirmPassword.error.value }"
              placeholder="Confirm your password"
            />
            <p v-if="confirmPassword.error.value" class="mt-1 text-sm text-red-600">
              {{ confirmPassword.error.value }}
            </p>
          </div>
        </div>

        <div v-if="error" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ error }}
              </h3>
            </div>
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="isLoading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ isLoading ? 'Creating account...' : 'Create account' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'
import { useValidation } from '@/composables/useValidation'

const { registerAndRedirect, isLoading, error, clearError } = useAuth()
const { createField, rules, validateForm } = useValidation()

const name = createField('', [
  rules.required('Name is required'),
  rules.minLength(2, 'Name must be at least 2 characters')
])

const email = createField('', [
  rules.required('Email is required'),
  rules.email()
])

const password = createField('', [
  rules.required('Password is required'),
  rules.minLength(6, 'Password must be at least 6 characters')
])

const confirmPassword = createField('', [
  rules.required('Please confirm your password'),
  rules.match(password.value, 'Passwords do not match')
])

const handleSubmit = async () => {
  clearError()
  
  if (!validateForm([name, email, password, confirmPassword])) {
    return
  }

  try {
    await registerAndRedirect({
      name: name.value.value,
      email: email.value.value,
      password: password.value.value,
      confirmPassword: confirmPassword.value.value
    })
  } catch (err) {
    // Error is handled by the auth store
  }
}
</script>

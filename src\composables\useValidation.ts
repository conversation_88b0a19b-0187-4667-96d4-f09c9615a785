import { ref, computed, type Ref } from 'vue'

export interface ValidationRule {
  message: string
  validator: (value: any) => boolean
}

export interface FieldValidation {
  value: Ref<string>
  error: Ref<string | null>
  isValid: Ref<boolean>
  validate: () => boolean
  reset: () => void
}

export const useValidation = () => {
  const createField = (initialValue = '', rules: ValidationRule[] = []): FieldValidation => {
    const value = ref(initialValue)
    const error = ref<string | null>(null)

    const isValid = computed(() => !error.value)

    const validate = (): boolean => {
      error.value = null
      
      for (const rule of rules) {
        if (!rule.validator(value.value)) {
          error.value = rule.message
          return false
        }
      }
      
      return true
    }

    const reset = () => {
      value.value = initialValue
      error.value = null
    }

    return {
      value,
      error,
      isValid,
      validate,
      reset
    }
  }

  // Common validation rules
  const rules = {
    required: (message = 'This field is required'): ValidationRule => ({
      message,
      validator: (value: string) => !!value.trim()
    }),

    email: (message = 'Please enter a valid email address'): ValidationRule => ({
      message,
      validator: (value: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(value)
      }
    }),

    minLength: (min: number, message?: string): ValidationRule => ({
      message: message || `Must be at least ${min} characters`,
      validator: (value: string) => value.length >= min
    }),

    maxLength: (max: number, message?: string): ValidationRule => ({
      message: message || `Must be no more than ${max} characters`,
      validator: (value: string) => value.length <= max
    }),

    match: (otherValue: Ref<string>, message = 'Values do not match'): ValidationRule => ({
      message,
      validator: (value: string) => value === otherValue.value
    })
  }

  const validateForm = (fields: FieldValidation[]): boolean => {
    let isFormValid = true
    
    fields.forEach(field => {
      if (!field.validate()) {
        isFormValid = false
      }
    })
    
    return isFormValid
  }

  return {
    createField,
    rules,
    validateForm
  }
}

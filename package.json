{"name": "vue-auth-dashboard", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.3.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "postcss": "^8.5.4", "prettier": "3.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}
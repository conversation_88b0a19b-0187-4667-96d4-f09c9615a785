<template>
  <DashboardLayout>
    <div class="space-y-6">
      <!-- Page header -->
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Analytics</h1>
        <p class="mt-1 text-sm text-gray-500">
          Track performance metrics and user engagement
        </p>
      </div>

      <!-- Metrics overview -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <div
          v-for="metric in metrics"
          :key="metric.name"
          class="bg-white overflow-hidden shadow rounded-lg"
        >
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <component
                  :is="metric.icon"
                  class="h-6 w-6 text-gray-400"
                />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ metric.name }}
                  </dt>
                  <dd>
                    <div class="text-lg font-medium text-gray-900">
                      {{ metric.value }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span
                class="font-medium"
                :class="[
                  metric.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                ]"
              >
                {{ metric.changeType === 'increase' ? '+' : '-' }}{{ metric.change }}
              </span>
              <span class="text-gray-500"> vs last week</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Traffic chart -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Website Traffic
            </h3>
            <div class="mt-5">
              <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div class="text-center">
                  <ChartBarIcon class="mx-auto h-12 w-12 text-gray-400" />
                  <p class="mt-2 text-sm text-gray-500">Chart placeholder</p>
                  <p class="text-xs text-gray-400">Integration with Chart.js or similar</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- User engagement -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              User Engagement
            </h3>
            <div class="mt-5">
              <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div class="text-center">
                  <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
                  <p class="mt-2 text-sm text-gray-500">Engagement metrics</p>
                  <p class="text-xs text-gray-400">Session duration, page views, etc.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top pages table -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Top Pages
          </h3>
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                    Page
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Views
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Unique Visitors
                  </th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                    Bounce Rate
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <tr v-for="page in topPages" :key="page.path">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                    {{ page.path }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {{ page.views.toLocaleString() }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {{ page.uniqueVisitors.toLocaleString() }}
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {{ page.bounceRate }}%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </DashboardLayout>
</template>

<script setup lang="ts">
import DashboardLayout from '@/components/DashboardLayout.vue'
import {
  ChartBarIcon,
  UsersIcon,
  EyeIcon,
  ClockIcon,
  CursorArrowRaysIcon
} from '@heroicons/vue/24/outline'

const metrics = [
  {
    name: 'Page Views',
    value: '45,231',
    icon: EyeIcon,
    change: '12%',
    changeType: 'increase'
  },
  {
    name: 'Unique Visitors',
    value: '12,543',
    icon: UsersIcon,
    change: '8%',
    changeType: 'increase'
  },
  {
    name: 'Avg. Session Duration',
    value: '4m 32s',
    icon: ClockIcon,
    change: '3%',
    changeType: 'decrease'
  },
  {
    name: 'Click-through Rate',
    value: '3.24%',
    icon: CursorArrowRaysIcon,
    change: '15%',
    changeType: 'increase'
  }
]

const topPages = [
  {
    path: '/dashboard',
    views: 12543,
    uniqueVisitors: 8932,
    bounceRate: 23.4
  },
  {
    path: '/users',
    views: 8765,
    uniqueVisitors: 6543,
    bounceRate: 18.7
  },
  {
    path: '/analytics',
    views: 5432,
    uniqueVisitors: 4321,
    bounceRate: 31.2
  },
  {
    path: '/settings',
    views: 3210,
    uniqueVisitors: 2876,
    bounceRate: 42.1
  }
]
</script>

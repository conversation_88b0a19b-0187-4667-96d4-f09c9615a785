<template>
  <div class="bg-white shadow rounded-lg p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">
      Supabase Integration Status
    </h3>
    
    <div class="space-y-3">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <component
            :is="configStatus.icon"
            :class="configStatus.color"
            class="h-5 w-5"
          />
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900">
            Configuration
          </p>
          <p class="text-sm text-gray-500">
            {{ configStatus.message }}
          </p>
        </div>
      </div>

      <div class="flex items-center">
        <div class="flex-shrink-0">
          <component
            :is="connectionStatus.icon"
            :class="connectionStatus.color"
            class="h-5 w-5"
          />
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900">
            Connection
          </p>
          <p class="text-sm text-gray-500">
            {{ connectionStatus.message }}
          </p>
        </div>
      </div>
    </div>

    <div v-if="!isConfigured" class="mt-4 p-4 bg-yellow-50 rounded-md">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5 text-yellow-400" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">
            Setup Required
          </h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>To enable Supabase authentication:</p>
            <ol class="list-decimal list-inside mt-2 space-y-1">
              <li>Create a Supabase project</li>
              <li>Update your <code class="bg-yellow-100 px-1 rounded">.env.local</code> file</li>
              <li>Run the database schema</li>
            </ol>
            <p class="mt-2">
              See <code class="bg-yellow-100 px-1 rounded">SUPABASE_SETUP.md</code> for detailed instructions.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { supabase } from '@/lib/supabase'
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

const isConfigured = ref(false)
const isConnected = ref(false)
const isLoading = ref(true)

const configStatus = computed(() => {
  if (isLoading.value) {
    return {
      icon: ExclamationTriangleIcon,
      color: 'text-gray-400',
      message: 'Checking configuration...'
    }
  }
  
  if (isConfigured.value) {
    return {
      icon: CheckCircleIcon,
      color: 'text-green-500',
      message: 'Environment variables configured'
    }
  }
  
  return {
    icon: XCircleIcon,
    color: 'text-red-500',
    message: 'Missing environment variables'
  }
})

const connectionStatus = computed(() => {
  if (isLoading.value) {
    return {
      icon: ExclamationTriangleIcon,
      color: 'text-gray-400',
      message: 'Testing connection...'
    }
  }
  
  if (isConnected.value) {
    return {
      icon: CheckCircleIcon,
      color: 'text-green-500',
      message: 'Successfully connected to Supabase'
    }
  }
  
  return {
    icon: XCircleIcon,
    color: 'text-red-500',
    message: 'Unable to connect to Supabase'
  }
})

onMounted(async () => {
  try {
    // Check if environment variables are configured
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY
    
    isConfigured.value = !!(supabaseUrl && supabaseKey && 
      supabaseUrl !== 'your_supabase_project_url' && 
      supabaseKey !== 'your_supabase_anon_key')
    
    if (isConfigured.value) {
      // Test connection by trying to get session
      const { data, error } = await supabase.auth.getSession()
      isConnected.value = !error
    }
  } catch (error) {
    console.error('Error checking Supabase status:', error)
    isConnected.value = false
  } finally {
    isLoading.value = false
  }
})
</script>

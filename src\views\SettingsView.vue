<template>
  <DashboardLayout>
    <div class="space-y-6">
      <!-- Page header -->
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage your application preferences and configuration
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Settings navigation -->
        <div class="lg:col-span-1">
          <nav class="space-y-1">
            <button
              v-for="item in settingsNavigation"
              :key="item.name"
              @click="activeSection = item.id"
              class="w-full text-left group rounded-md px-3 py-2 flex items-center text-sm font-medium transition-colors duration-150"
              :class="[
                activeSection === item.id
                  ? 'bg-primary-100 text-primary-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <component
                :is="item.icon"
                class="flex-shrink-0 -ml-1 mr-3 h-5 w-5"
                :class="[
                  activeSection === item.id
                    ? 'text-primary-500'
                    : 'text-gray-400 group-hover:text-gray-500'
                ]"
              />
              {{ item.name }}
            </button>
          </nav>
        </div>

        <!-- Settings content -->
        <div class="lg:col-span-2">
          <!-- General settings -->
          <div v-if="activeSection === 'general'" class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                General Settings
              </h3>
              <form class="space-y-4">
                <div>
                  <label for="siteName" class="block text-sm font-medium text-gray-700">
                    Site Name
                  </label>
                  <input
                    id="siteName"
                    type="text"
                    v-model="settings.siteName"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label for="siteDescription" class="block text-sm font-medium text-gray-700">
                    Site Description
                  </label>
                  <textarea
                    id="siteDescription"
                    rows="3"
                    v-model="settings.siteDescription"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  ></textarea>
                </div>
                <div class="flex items-center">
                  <input
                    id="maintenanceMode"
                    type="checkbox"
                    v-model="settings.maintenanceMode"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label for="maintenanceMode" class="ml-2 block text-sm text-gray-900">
                    Enable maintenance mode
                  </label>
                </div>
              </form>
            </div>
          </div>

          <!-- Security settings -->
          <div v-if="activeSection === 'security'" class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                Security Settings
              </h3>
              <form class="space-y-4">
                <div class="flex items-center">
                  <input
                    id="twoFactor"
                    type="checkbox"
                    v-model="settings.twoFactorAuth"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label for="twoFactor" class="ml-2 block text-sm text-gray-900">
                    Enable two-factor authentication
                  </label>
                </div>
                <div>
                  <label for="sessionTimeout" class="block text-sm font-medium text-gray-700">
                    Session Timeout (minutes)
                  </label>
                  <input
                    id="sessionTimeout"
                    type="number"
                    v-model="settings.sessionTimeout"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  />
                </div>
                <div class="flex items-center">
                  <input
                    id="passwordExpiry"
                    type="checkbox"
                    v-model="settings.passwordExpiry"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label for="passwordExpiry" class="ml-2 block text-sm text-gray-900">
                    Require password change every 90 days
                  </label>
                </div>
              </form>
            </div>
          </div>

          <!-- Notifications settings -->
          <div v-if="activeSection === 'notifications'" class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                Notification Settings
              </h3>
              <form class="space-y-4">
                <div class="flex items-center">
                  <input
                    id="emailNotifications"
                    type="checkbox"
                    v-model="settings.emailNotifications"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label for="emailNotifications" class="ml-2 block text-sm text-gray-900">
                    Email notifications
                  </label>
                </div>
                <div class="flex items-center">
                  <input
                    id="pushNotifications"
                    type="checkbox"
                    v-model="settings.pushNotifications"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label for="pushNotifications" class="ml-2 block text-sm text-gray-900">
                    Push notifications
                  </label>
                </div>
                <div class="flex items-center">
                  <input
                    id="smsNotifications"
                    type="checkbox"
                    v-model="settings.smsNotifications"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label for="smsNotifications" class="ml-2 block text-sm text-gray-900">
                    SMS notifications
                  </label>
                </div>
              </form>
            </div>
          </div>

          <!-- Save button -->
          <div class="mt-6">
            <button
              @click="saveSettings"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  </DashboardLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DashboardLayout from '@/components/DashboardLayout.vue'
import {
  CogIcon,
  ShieldCheckIcon,
  BellIcon
} from '@heroicons/vue/24/outline'

const activeSection = ref('general')

const settingsNavigation = [
  { name: 'General', id: 'general', icon: CogIcon },
  { name: 'Security', id: 'security', icon: ShieldCheckIcon },
  { name: 'Notifications', id: 'notifications', icon: BellIcon }
]

const settings = ref({
  siteName: 'My Dashboard',
  siteDescription: 'A modern dashboard application built with Vue 3',
  maintenanceMode: false,
  twoFactorAuth: false,
  sessionTimeout: 30,
  passwordExpiry: true,
  emailNotifications: true,
  pushNotifications: false,
  smsNotifications: false
})

const saveSettings = () => {
  // Here you would typically save to an API
  console.log('Saving settings:', settings.value)
  alert('Settings saved successfully!')
}
</script>
